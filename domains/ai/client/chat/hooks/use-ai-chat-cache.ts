import { useLocalStorageState } from 'ahooks';
import type { AIChatContextVO } from '@bika/types/ai/vo';
import { type TalkBO } from '@bika/types/space/bo';
import { useSpaceContext } from '@bika/types/space/context';
import { PreviewAttachment } from '../attachment/attachment-preview';

export type AIChatCacheSelector = { type: 'agent'; agent: TalkBO } | { type: 'copilot'; copilot: TalkBO };

/**
 *
 * Fetch the cached AI chat ID from local storage.
 *
 * @param selector
 * @returns
 */
export function useAIChatCache(selector: AIChatCacheSelector): [string | undefined, (id: string | undefined) => void] {
  const spaceContext = useSpaceContext();
  let talk: TalkBO;
  if (selector.type === 'agent') {
    talk = selector.agent;
  } else {
    talk = selector.copilot;
  }
  const talkType = talk.type;
  // talk对应普通的各种 ai 聊天，copilot对应 talk 里的附带聊天
  // 'ai-chat-cache-<spaceId|standalone>-<talk|copilot>-<node|expert|...>'
  const cacheName = `ai-chat-cache-${spaceContext?.data.id || 'website'}-${selector.type}-${talkType}`;

  const [chatIds, setChatIds] = useLocalStorageState<Record<string, string | undefined>>(cacheName, {
    defaultValue: {},
  });

  let chatId: string;
  switch (talkType) {
    case 'node':
      chatId = talk.nodeId;
      break;
    case 'expert':
      chatId = talk.expertKey;
      break;
    case 'unit':
      chatId = talk.unitId;
      break;
    default:
      throw new Error(`Unsupported talk type: ${talkType}`);
  }

  const cacheChatId = chatIds?.[chatId];
  const setCacheChatId = (id: string | undefined) => {
    setChatIds(() => ({
      ...chatIds,
      [chatId]: id,
    }));
  };

  return [cacheChatId, setCacheChatId];
}

export interface ChatCacheData {
  data: {
    chatId: string;
    context?: AIChatContextVO[];
  };
  api: {
    setAttachments: (attachments: PreviewAttachment[] | ((prev: PreviewAttachment[]) => PreviewAttachment[])) => void;
  };
}

export function useAIChatData(selector: AIChatCacheSelector): ChatCacheData {
  const [chatId, setChatId] = useAIChatCache(selector);

  return {
    data: {
      chatId: chatId ?? '',
      context: [],
    },
    api: {
      setAttachments: () => {},
    },
  };
}
